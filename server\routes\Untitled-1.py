def min_edit_distance(str1, str2):
    m = len(str1)
    n = len(str2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    for i in range(m + 1):
        dp[i][0] = i  
    for j in range(n + 1):
        dp[0][j] = j  
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if str1[i - 1] == str2[j - 1]:
                dp[i][j] = dp[i - 1][j - 1]  
            else:
                dp[i][j] = 1 + min(
                    dp[i - 1][j],  
                    dp[i][j - 1],  
                    dp[i - 1][j - 1]  
                )
    return dp[m][n]
test_cases = [
    ("kitten", "sitting"),  # Substitutions and insertions
    ("intention", "execution"),  # Substitutions and deletions
    ("flaw", "lawn"),  # Substitutions
    ("apple", "aple"),  # Deletion
    ("book", "books"),  # Insertion
    ("abc", "def"),  # All substitutions
    ("", "abc"),  # All insertions
    ("abc", "")  # All deletions
]
for str1, str2 in test_cases:
    distance = min_edit_distance(str1, str2)
    print(f"MED between '{str1}' and '{str2}': {distance}")
