import React, { useState, useRef, useEffect } from 'react';
import { Box, Button, Typography, Paper, Grid, CircularProgress, Alert, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { styled } from '@mui/material/styles';
import axios from 'axios';
import { BASE_URL } from '../../../../helper';
import { browserCompatibility, checkBrowserCompatibility } from '../../../../utils/browserCompatibility';

const VerificationPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(2),
  textAlign: 'center',
  borderRadius: '10px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
}));

const ImagePreview = styled('img')({
  width: '100%',
  maxHeight: '200px',
  objectFit: 'contain',
  marginTop: '10px',
  borderRadius: '5px'
});

const VideoContainer = styled(Box)({
  position: 'relative',
  width: '100%',
  maxHeight: '200px',
  borderRadius: '5px',
  backgroundColor: '#f0f0f0',
  overflow: 'hidden'
});

const VideoPreview = styled('video')({
  width: '100%',
  maxHeight: '200px',
  borderRadius: '5px',
  backgroundColor: '#f0f0f0',
  transform: 'scaleX(-1)' // Mirror the video horizontally
});

const SimilarityMeter = styled(Box)(({ theme, percentage }) => ({
  marginTop: theme.spacing(2),
  padding: theme.spacing(1),
  borderRadius: '5px',
  backgroundColor:
    percentage >= 60 && percentage <= 80
      ? theme.palette.success.light
      : theme.palette.error.light,
  color:
    percentage >= 60 && percentage <= 80
      ? theme.palette.success.dark
      : theme.palette.error.dark,
  textAlign: 'center',
  fontWeight: 'bold'
}));

const VerifyVoter = ({ onVerificationComplete }) => {
  const [selfieImage, setSelfieImage] = useState(null);
  const [idImage, setIdImage] = useState(null);
  const [videoFrame, setVideoFrame] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [similarityPercentage, setSimilarityPercentage] = useState(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [compatibilityChecked, setCompatibilityChecked] = useState(false);
  const [compatibilityReport, setCompatibilityReport] = useState(null);
  const [showCompatibilityDialog, setShowCompatibilityDialog] = useState(false);
  const [retryAttempts, setRetryAttempts] = useState(0);

  // Refs for video elements
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  // Initialize compatibility check when component mounts
  useEffect(() => {
    const runCompatibilityCheck = async () => {
      try {
        const { results, report } = await checkBrowserCompatibility();
        setCompatibilityReport(report);
        setCompatibilityChecked(true);

        console.log('Browser compatibility check:', report);

        if (!report.compatible) {
          setError('Your browser or device is not compatible with video capture. Please check the compatibility report for details.');
          setShowCompatibilityDialog(true);
        } else if (report.warnings.length > 0) {
          console.warn('Compatibility warnings:', report.warnings);
        }
      } catch (error) {
        console.error('Compatibility check failed:', error);
        setError('Failed to check browser compatibility. Please ensure you are using a modern browser.');
        setCompatibilityChecked(true);
      }
    };

    runCompatibilityCheck();

    return () => {
      // Cleanup function to stop camera when component unmounts
      cleanupVideoResources();
    };
  }, []);

  // Enhanced cleanup function
  const cleanupVideoResources = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop();
        console.log('Stopped track:', track.kind);
      });
      streamRef.current = null;
    }

    if (videoRef.current) {
      videoRef.current.srcObject = null;
      videoRef.current.load(); // Reset video element
    }

    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    }

    setCameraActive(false);
  };

  const startCamera = async (retryWithBasicConstraints = false) => {
    try {
      setError(null); // Clear any previous errors
      console.log('Requesting camera access...');

      // Check compatibility first
      if (!compatibilityChecked) {
        setError('Please wait for compatibility check to complete.');
        return;
      }

      if (compatibilityReport && !compatibilityReport.compatible) {
        setError('Your browser or device is not compatible with video capture.');
        setShowCompatibilityDialog(true);
        return;
      }

      // Progressive constraint fallback
      let constraints;
      if (retryWithBasicConstraints || retryAttempts > 0) {
        constraints = {
          video: {
            width: { ideal: 320, min: 240 },
            height: { ideal: 240, min: 180 }
          },
          audio: false
        };
        console.log('Using basic constraints for retry');
      } else {
        constraints = {
          video: {
            width: { ideal: 640, min: 480 },
            height: { ideal: 480, min: 360 },
            facingMode: 'user',
            frameRate: { ideal: 30, min: 15 }
          },
          audio: false
        };
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('Camera access granted:', stream);

      streamRef.current = stream;

      // Enhanced video setup with promise-based approach
      if (videoRef.current) {
        await setupVideoElement(videoRef.current, stream);
      }

      setCameraActive(true);
      setRetryAttempts(0); // Reset retry counter on success

    } catch (error) {
      console.error('Error accessing camera:', error);
      handleCameraError(error);
    }
  };

  // Enhanced video element setup
  const setupVideoElement = (videoElement, stream) => {
    return new Promise((resolve, reject) => {
      videoElement.srcObject = stream;

      videoElement.onloadedmetadata = () => {
        console.log('Video metadata loaded');
        videoElement.play()
          .then(() => {
            console.log('Video playing successfully');
            resolve();
          })
          .catch(reject);
      };

      videoElement.onerror = (error) => {
        console.error('Video element error:', error);
        reject(new Error('Failed to load video'));
      };

      // Set video attributes
      videoElement.autoplay = true;
      videoElement.playsInline = true;
      videoElement.muted = true;
    });
  };

  // Enhanced error handling
  const handleCameraError = (error) => {
    const errorMessage = browserCompatibility.getCameraErrorMessage(error);
    setError(errorMessage);

    // Offer retry with basic constraints for certain errors
    if (error.name === 'OverconstrainedError' && retryAttempts < 2) {
      console.log('Retrying with basic constraints...');
      setRetryAttempts(prev => prev + 1);
      setTimeout(() => startCamera(true), 1000);
    }
  };

  const stopCamera = () => {
    cleanupVideoResources();
  };

  const captureVideoFrame = () => {
    if (!videoRef.current || !canvasRef.current) {
      console.error('Video or canvas reference not available');
      setError('Cannot capture frame: video not initialized properly');
      return;
    }

    try {
      console.log('Attempting to capture video frame...');

      const canvas = canvasRef.current;
      const video = videoRef.current;

      // Check if video is playing and has dimensions
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.error('Video dimensions are zero. Video may not be playing yet.');
        setError('Cannot capture frame: video not ready. Please wait a moment and try again.');
        return;
      }

      console.log(`Video dimensions: ${video.videoWidth}x${video.videoHeight}`);

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the current video frame to the canvas
      const context = canvas.getContext('2d');

      // Clear the canvas first
      context.clearRect(0, 0, canvas.width, canvas.height);

      // Mirror the image horizontally to match the mirrored video display
      context.translate(canvas.width, 0);
      context.scale(-1, 1);
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Reset transformation matrix to default
      context.setTransform(1, 0, 0, 1, 0, 0);

      // Add a visual indicator that the frame was captured
      const captureIndicator = document.createElement('div');
      captureIndicator.style.position = 'absolute';
      captureIndicator.style.top = '0';
      captureIndicator.style.left = '0';
      captureIndicator.style.width = '100%';
      captureIndicator.style.height = '100%';
      captureIndicator.style.backgroundColor = 'white';
      captureIndicator.style.opacity = '0.5';

      // Append to video container and remove after a short flash
      const videoContainer = videoRef.current.parentElement;
      if (videoContainer) {
        videoContainer.appendChild(captureIndicator);
        setTimeout(() => {
          videoContainer.removeChild(captureIndicator);
        }, 150);
      }

      // Convert canvas to blob with higher quality
      canvas.toBlob((blob) => {
        if (blob) {
          console.log(`Captured frame as blob: ${blob.size} bytes`);

          // Create a File object from the blob
          const file = new File([blob], 'video-frame.jpg', { type: 'image/jpeg' });
          setVideoFrame(file);

          // Provide user feedback
          setError(null); // Clear any previous errors

          // Stop the camera after successful capture
          stopCamera();
        } else {
          console.error('Failed to create blob from canvas');
          setError('Failed to capture video frame. Please try again.');
        }
      }, 'image/jpeg', 0.95); // 95% quality
    } catch (error) {
      console.error('Error capturing video frame:', error);
      setError(`Error capturing video frame: ${error.message}`);
    }
  };

  const handleIdImageChange = (event) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setIdImage(file);
    }
  };

  const handleSelfieImageChange = (event) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelfieImage(file);
    }
  };

  const handleVerification = async () => {
    // Validate all required inputs
    if (!selfieImage || !idImage) {
      setError('Both selfie and ID image are required');
      return;
    }

    // Make video frame mandatory
    if (!videoFrame) {
      setError('Live video capture is required. Please start the camera and capture a frame.');
      return;
    }

    setLoading(true);
    setError(null);
    setSimilarityPercentage(null);

    try {
      const formData = new FormData();
      formData.append('selfie', selfieImage);
      formData.append('idImage', idImage);
      formData.append('videoFrame', videoFrame);

      console.log('Submitting verification request with all required images...');

      const response = await axios.post(`${BASE_URL}/verify-voter`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        // Set similarity percentage
        setSimilarityPercentage(response.data.similarityPercentage);

        // Show detailed comparison results if available
        console.log('Verification successful:', response.data);

        // Display a success message with the similarity percentage
        const successMessage = `Verification successful! Your face similarity is ${response.data.similarityPercentage}%`;
        alert(successMessage);

        // Pass verification ID to parent component
        onVerificationComplete(response.data.verificationId);
      } else {
        // Handle case where the server returns success: false but not an error status
        setError(response.data.message || 'Verification failed. Please try again.');

        // Still show similarity percentage if available
        if (response.data.similarityPercentage) {
          setSimilarityPercentage(response.data.similarityPercentage);
        }
      }
    } catch (error) {
      console.error('Verification error:', error);

      // Get detailed error message from response
      const errorMessage = error.response?.data?.message || 'Verification failed. Please try again.';
      const verificationStep = error.response?.data?.verificationStep || 'unknown';
      const imageType = error.response?.data?.image || null;

      // Set similarity percentage if available in the error response
      if (error.response?.data?.similarityPercentage) {
        setSimilarityPercentage(error.response.data.similarityPercentage);
      }

      // Set specific error message based on verification step and image type
      let detailedError = errorMessage;

      if (verificationStep === 'face_matching') {
        detailedError = `Face matching failed: ${errorMessage}`;
      } else if (verificationStep === 'face_detection') {
        if (imageType === 'selfie') {
          detailedError = `Could not detect a face in your selfie. Please ensure your face is clearly visible and try again.`;
        } else if (imageType === 'id') {
          detailedError = `Could not detect a face in your ID document. Please ensure the ID has a clear face photo and try again.`;
        } else if (imageType === 'video') {
          detailedError = `Could not detect a face in your video frame. Please ensure your face is clearly visible and try again.`;
        } else {
          detailedError = `Face detection failed: ${errorMessage}`;
        }
      } else if (verificationStep === 'security_violation') {
        detailedError = `Security violation detected: ${errorMessage}`;
      } else if (verificationStep === 'file_validation') {
        detailedError = `File validation failed: ${errorMessage}`;
      }

      setError(detailedError);
    } finally {
      setLoading(false);

      // Stop the camera after verification
      if (cameraActive) {
        stopCamera();
      }
    }
  };

  const resetVerification = () => {
    setSelfieImage(null);
    setIdImage(null);
    setVideoFrame(null);
    setError(null);
    setSimilarityPercentage(null);

    // Stop camera if active
    if (cameraActive) {
      stopCamera();
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', my: 4 }}>
      <Typography variant="h4" gutterBottom align="center" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
        Voter Verification
      </Typography>

      <Typography variant="body1" paragraph align="center">
        Please complete the verification process before voting. We need to verify your identity with a selfie, a government ID, and a live video capture.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {similarityPercentage !== null && (
        <SimilarityMeter percentage={similarityPercentage}>
          <Typography variant="body1">
            Face Similarity: {similarityPercentage}%
          </Typography>
          <Typography variant="body2">
            {similarityPercentage >= 60 && similarityPercentage <= 80
              ? 'Match is within acceptable range (60%-80%)'
              : 'Match is outside acceptable range (60%-80%)'}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={similarityPercentage}
            color={similarityPercentage >= 60 && similarityPercentage <= 80 ? "success" : "error"}
            sx={{ mt: 1, height: 10, borderRadius: 5 }}
          />
        </SimilarityMeter>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <VerificationPaper>
            <Typography variant="h6" gutterBottom>
              Take a Selfie
            </Typography>

            <Box>
              {selfieImage ? (
                <ImagePreview src={URL.createObjectURL(selfieImage)} alt="Selfie preview" />
              ) : (
                <Box
                  sx={{
                    height: 200,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px dashed #ccc',
                    borderRadius: '5px'
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    No selfie uploaded yet
                  </Typography>
                </Box>
              )}
              <Button
                variant="contained"
                component="label"
                sx={{ mt: 2 }}
                disabled={!!selfieImage}
              >
                {selfieImage ? 'Selfie Uploaded' : 'Upload Selfie'}
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleSelfieImageChange}
                />
              </Button>
            </Box>
          </VerificationPaper>
        </Grid>

        <Grid item xs={12} md={4}>
          <VerificationPaper>
            <Typography variant="h6" gutterBottom>
              Upload ID Document
            </Typography>
            <Typography variant="body2" paragraph>
              Please upload a government-issued ID (Aadhar, Voter ID, or Driving License)
            </Typography>

            {idImage ? (
              <ImagePreview src={URL.createObjectURL(idImage)} alt="ID preview" />
            ) : (
              <Box
                sx={{
                  height: 200,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px dashed #ccc',
                  borderRadius: '5px'
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  No ID uploaded yet
                </Typography>
              </Box>
            )}

            <Button
              variant="contained"
              component="label"
              sx={{ mt: 2 }}
              disabled={!!idImage}
            >
              {idImage ? 'ID Uploaded' : 'Upload ID'}
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={handleIdImageChange}
              />
            </Button>
          </VerificationPaper>
        </Grid>

        <Grid item xs={12} md={4}>
          <VerificationPaper>
            <Typography variant="h6" gutterBottom>
              Live Video Capture
            </Typography>
            <Typography variant="body2" paragraph>
              We need to verify that you match your ID in real-time
            </Typography>

            <VideoContainer sx={{ position: 'relative' }}>
              {cameraActive ? (
                <>
                  <VideoPreview
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 5,
                      left: 5,
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                  >
                    <span style={{ color: 'red', fontSize: '8px' }}>●</span> LIVE
                  </Box>
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      padding: '4px',
                      textAlign: 'center',
                      fontSize: '12px'
                    }}
                  >
                    Look directly at the camera and click "Capture Frame"
                  </Box>
                </>
              ) : videoFrame ? (
                <Box sx={{ position: 'relative' }}>
                  <ImagePreview src={URL.createObjectURL(videoFrame)} alt="Video frame preview" />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 5,
                      right: 5,
                      backgroundColor: 'rgba(0,128,0,0.7)',
                      color: 'white',
                      padding: '2px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                  >
                    ✓ CAPTURED
                  </Box>
                </Box>
              ) : (
                <Box
                  sx={{
                    height: 200,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px dashed #ccc',
                    borderRadius: '5px',
                    padding: 2
                  }}
                >
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    No video frame captured yet
                  </Typography>
                  <Typography variant="caption" color="text.secondary" align="center">
                    Click "Start Camera" to enable your webcam
                  </Typography>
                </Box>
              )}
              <canvas
                ref={canvasRef}
                style={{ display: 'none' }}
              />
            </VideoContainer>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 1, flexDirection: 'column', alignItems: 'center' }}>
              {!cameraActive ? (
                <Button
                  variant="contained"
                  onClick={startCamera}
                  disabled={loading || videoFrame}
                  startIcon={<span role="img" aria-label="camera">📷</span>}
                  sx={{
                    minWidth: '180px',
                    bgcolor: videoFrame ? 'success.main' : 'primary.main',
                    '&:hover': {
                      bgcolor: videoFrame ? 'success.dark' : 'primary.dark',
                    }
                  }}
                >
                  {videoFrame ? 'Frame Captured' : 'Start Camera'}
                </Button>
              ) : (
                <>
                  <Button
                    variant="contained"
                    onClick={captureVideoFrame}
                    disabled={loading}
                    color="success"
                    startIcon={<span role="img" aria-label="capture">📸</span>}
                    sx={{
                      minWidth: '180px',
                      animation: 'pulse 1.5s infinite',
                      '@keyframes pulse': {
                        '0%': {
                          boxShadow: '0 0 0 0 rgba(76, 175, 80, 0.7)',
                        },
                        '70%': {
                          boxShadow: '0 0 0 10px rgba(76, 175, 80, 0)',
                        },
                        '100%': {
                          boxShadow: '0 0 0 0 rgba(76, 175, 80, 0)',
                        },
                      },
                    }}
                  >
                    Capture Frame
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={stopCamera}
                    disabled={loading}
                    size="small"
                    sx={{ mt: 1 }}
                    startIcon={<span role="img" aria-label="stop">⏹️</span>}
                  >
                    Cancel
                  </Button>
                </>
              )}

              {videoFrame && (
                <Button
                  variant="outlined"
                  onClick={() => {
                    setVideoFrame(null);
                    setError(null);
                  }}
                  size="small"
                  color="primary"
                  sx={{ mt: 1 }}
                  startIcon={<span role="img" aria-label="retake">🔄</span>}
                >
                  Retake Video Frame
                </Button>
              )}

              {!cameraActive && !videoFrame && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
                  You must capture a video frame to complete verification
                </Typography>
              )}
            </Box>
          </VerificationPaper>
        </Grid>
      </Grid>

      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, gap: 2 }}>
        <Button
          variant="outlined"
          onClick={resetVerification}
          disabled={loading}
        >
          Reset
        </Button>
        <Button
          variant="contained"
          onClick={handleVerification}
          disabled={!selfieImage || !idImage || !videoFrame || loading}
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
          color={selfieImage && idImage && videoFrame ? "primary" : "inherit"}
          sx={{
            position: 'relative',
            '&::after': (!selfieImage || !idImage || !videoFrame) ? {
              content: '"All 3 images required"',
              position: 'absolute',
              bottom: '-20px',
              left: '50%',
              transform: 'translateX(-50%)',
              fontSize: '10px',
              whiteSpace: 'nowrap',
              color: 'error.main'
            } : {}
          }}
        >
          {loading ? 'Verifying...' : 'Verify Identity'}
        </Button>
      </Box>

      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 2 }}>
        <Typography variant="subtitle2" color="info.dark" align="center" sx={{ fontWeight: 'bold' }}>
          Important Security Information
        </Typography>
        <Typography variant="body2" color="info.dark" align="center">
          • For successful verification, face similarity must be between 60% and 80%
        </Typography>
        <Typography variant="body2" color="info.dark" align="center">
          • All three images (selfie, ID, and live video) are required and must match
        </Typography>
        <Typography variant="body2" color="info.dark" align="center">
          • Only one face should be visible in each image
        </Typography>
        <Typography variant="body2" color="info.dark" align="center">
          • Failed verification attempts are logged for security purposes
        </Typography>
      </Box>

      {/* Browser Compatibility Dialog */}
      <Dialog
        open={showCompatibilityDialog}
        onClose={() => setShowCompatibilityDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Browser Compatibility Issues</DialogTitle>
        <DialogContent>
          {compatibilityReport && (
            <Box>
              <Typography variant="h6" color="error" gutterBottom>
                Issues Found:
              </Typography>
              {compatibilityReport.issues.map((issue, index) => (
                <Typography key={index} variant="body2" color="error" sx={{ mb: 1 }}>
                  • {issue}
                </Typography>
              ))}

              {compatibilityReport.warnings.length > 0 && (
                <>
                  <Typography variant="h6" color="warning.main" gutterBottom sx={{ mt: 2 }}>
                    Warnings:
                  </Typography>
                  {compatibilityReport.warnings.map((warning, index) => (
                    <Typography key={index} variant="body2" color="warning.main" sx={{ mb: 1 }}>
                      • {warning}
                    </Typography>
                  ))}
                </>
              )}

              {compatibilityReport.recommendations.length > 0 && (
                <>
                  <Typography variant="h6" color="info.main" gutterBottom sx={{ mt: 2 }}>
                    Recommendations:
                  </Typography>
                  {compatibilityReport.recommendations.map((rec, index) => (
                    <Typography key={index} variant="body2" color="info.main" sx={{ mb: 1 }}>
                      • {rec}
                    </Typography>
                  ))}
                </>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCompatibilityDialog(false)}>
            Close
          </Button>
          <Button
            onClick={() => {
              setShowCompatibilityDialog(false);
              if (compatibilityReport && compatibilityReport.compatible) {
                startCamera();
              }
            }}
            variant="contained"
            disabled={compatibilityReport && !compatibilityReport.compatible}
          >
            Try Anyway
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VerifyVoter;
