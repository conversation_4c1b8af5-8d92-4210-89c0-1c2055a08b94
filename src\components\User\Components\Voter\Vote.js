import './Vote.css';
import UserNavbar from '../../../Navbar/UserNavbar';
import * as React from 'react';
import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import Button from '@mui/material/Button';
import ScrollReveal from "scrollreveal";
import Backdrop from '@mui/material/Backdrop';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import Fade from '@mui/material/Fade';
import Typography from '@mui/material/Typography';
import { BASE_URL } from '../../../../helper';
import Cookies from 'js-cookie';
import VerifyVoter from './VerifyVoter';
import SecureVotingSession from './SecureVotingSession';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 700,
    bgcolor: 'rgb(255, 255, 255)',
    border: '2px solid #000',
    boxShadow: 24,
    p: 4,
};


const StyledTableCell = styled(TableCell)(({ theme }) => ({
    [`&.${tableCellClasses.head}`]: {
        // backgroundColor: theme.palette.common,
        color: theme.palette.common.white,
        fontSize: 16,
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
    },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
    '&:nth-of-type(odd)': {
        backgroundColor: theme.palette.action.hover,
    },
    // hide last border
    '&:last-child td, &:last-child th': {
        border: 0,
    },
}));
const columns = [
    { id: 'fullname', label: `Candidate Name`, minWidth: 250, align: "left" },
    { id: 'party', label: 'Party', minWidth: 120 },
    { id: 'age', label: 'Age', minWidth: 180, align: "center" },
    { id: 'photo', label: 'Symbol', minWidth: 100, align: "right" },
    { id: 'action', label: '', minWidth: 200 },

];



export default function CustomizedTables() {
    const revealRefBottom = useRef(null);
    const revealRefLeft = useRef(null);
    const revealRefTop = useRef(null);
    const revealRefRight = useRef(null);

    useEffect(() => {
        ScrollReveal().reveal(revealRefBottom.current, {
            duration: 1000,
            delay: 300,
            distance: '50px',
            origin: 'bottom',
            easing: 'ease',
            reset: 'true',
        });
    }, []);

    useEffect(() => {
        ScrollReveal().reveal(revealRefRight.current, {
            duration: 1000,
            delay: 300,
            distance: '50px',
            origin: 'right',
            easing: 'ease',
            reset: 'true',
        });
    }, []);

    useEffect(() => {
        ScrollReveal().reveal(revealRefLeft.current, {
            duration: 1000,
            delay: 300,
            distance: '50px',
            origin: 'left',
            easing: 'ease',
            reset: 'true',
        });
    }, []);

    useEffect(() => {
        ScrollReveal().reveal(revealRefTop.current, {
            duration: 1000,
            delay: 300,
            distance: '50px',
            origin: 'top',
            easing: 'ease',
            reset: 'true',
        });
    }, []);
    const [candidate, setCandidate] = useState([]);
    const [voter, setVoter] = useState([]);
    const [isVerified, setIsVerified] = useState(false);
    const [verificationId, setVerificationId] = useState(null);
    const [showVerification, setShowVerification] = useState(false);
    const [verificationError, setVerificationError] = useState(null);
    const [showSecureVotingSession, setShowSecureVotingSession] = useState(false);
    const [selectedCandidateId, setSelectedCandidateId] = useState(null);
    const [securityViolation, setSecurityViolation] = useState(null);

    const voterid = Cookies.get('myCookie')

    useEffect(() => {
        axios.get(`${BASE_URL}/getCandidate`)
            .then((response) => setCandidate(response.data.candidate))
            .catch(err => console.error("Error fetching data: ", err));
    }, [])

    useEffect(() => {
        // const token = localStorage.getItem('token'); // Assuming token is stored in localStorage
        // console.log(token);
        axios.get(`${BASE_URL}/getVoterbyID/${voterid}`)
            .then((response) => {
                setVoter(response.data.voter);
            })
            .catch(error => {
                console.error('Error fetching user data:', error);
            });
    }, [voterid]);

    const [open, setOpen] = React.useState(false);
    const [voteSuccessful, setVoteSuccessful] = useState(true); // Track if vote was successful
    const handleOpen = () => {
        // Only open the congratulation modal if the vote was successful
        if (voteSuccessful) {
            setOpen(true);
        }
    };
    const handleClose = () => setOpen(false);

    const handleVerificationComplete = (verificationId) => {
        // Only proceed if we have a valid verification ID
        if (verificationId) {
            setVerificationId(verificationId);
            setIsVerified(true);
            setShowVerification(false);
            // Clear any previous verification errors
            setVerificationError(null);
        } else {
            // If no verification ID is provided, verification failed
            setIsVerified(false);
            setVerificationError('Verification failed. Please try again.');
        }
    };

    const startVerification = () => {
        setShowVerification(true);
    };

    const startSecureVotingSession = (id) => {
        setSelectedCandidateId(id);
        setShowSecureVotingSession(true);
        setVoteSuccessful(true); // Reset vote status when starting a new session
    };

    const handleSecurityViolation = (message, violationData = {}) => {
        setSecurityViolation(message);
        setShowSecureVotingSession(false);
        setVerificationError(`Security violation detected: ${message}`);

        // Mark the vote as unsuccessful to prevent showing congratulation message
        setVoteSuccessful(false);

        // Record the invalid vote if a candidate was selected
        if (selectedCandidateId) {
            // Prepare data for invalid vote record
            const invalidVoteData = {
                voterId: voter._id,
                candidateId: selectedCandidateId,
                violationType: violationData.violationType || 'other',
                violationDetails: violationData.violationDetails || message,
                evidenceData: violationData.evidenceData || null
            };

            // Send to server to record the invalid vote
            axios.post(`${BASE_URL}/recordInvalidVote`, invalidVoteData)
                .then(response => {
                    console.log('Invalid vote recorded:', response.data);
                })
                .catch(error => {
                    console.error('Error recording invalid vote:', error);
                });
        }
    };

    const handleVotingComplete = (id) => {
        // Close the secure voting session
        setShowSecureVotingSession(false);

        // Only proceed with voting if there was no security violation
        if (voteSuccessful) {
            // User is verified and monitored, proceed with voting
            voter.voteStatus = true;

            // Send verification ID with the vote
            axios.patch(`${BASE_URL}/getCandidate/${id}`, { verificationId })
                .then(response => {
                    // Update voter status
                    axios.patch(`${BASE_URL}/updateVoter/${voter._id}`, voter)
                        .then(() => {
                            handleOpen(); // This will only show if voteSuccessful is true
                        })
                        .catch(error => {
                            console.error('Error updating voter status:', error);
                            setVerificationError('Failed to update voter status. Please try again.');
                            setVoteSuccessful(false); // Mark as unsuccessful due to error
                        });
                })
                .catch(error => {
                    console.error('Error voting:', error);
                    setVoteSuccessful(false); // Mark as unsuccessful due to error
                    if (error.response && error.response.status === 403) {
                        setVerificationError(error.response.data.message || 'Verification required before voting');
                        setIsVerified(false);
                        startVerification();
                    } else {
                        setVerificationError('Error casting vote. Please try again.');
                    }
                });
        } else {
            // If vote was unsuccessful (security violation), show appropriate message
            setVerificationError('Vote was not cast due to security violations.');
        }
    };





    const handleVote = (id) => {
        // Check if user has already voted
        if (voter.voteStatus) {
            alert("You Have Already Voted");
            return;
        }

        // Check if user is verified
        if (!isVerified) {
            startVerification();
            return;
        }

        // Start secure voting session with continuous monitoring
        startSecureVotingSession(id);
    }




    return (
        <div className='Vote-Page'>
            <UserNavbar />
            <div className='candidate'>
                <h2 ref={revealRefLeft}>2024 India General Election</h2>
                <div className='Heading1' ref={revealRefRight}>
                    <p><span>GIVE</span> Your Vote</p>
                </div>

                {showVerification && (
                    <Box sx={{ mt: 4, mb: 4 }}>
                        <VerifyVoter onVerificationComplete={handleVerificationComplete} />
                    </Box>
                )}

                {verificationError && (
                    <Box sx={{ mt: 2, mb: 2, p: 2, bgcolor: '#ffebee', borderRadius: 1 }}>
                        <Typography color="error">{verificationError}</Typography>
                    </Box>
                )}

                {showSecureVotingSession && (
                    <Box sx={{ mt: 4, mb: 4 }}>
                        <SecureVotingSession
                            onVotingComplete={handleVotingComplete}
                            candidateId={selectedCandidateId}
                            onSecurityViolation={handleSecurityViolation}
                        />
                    </Box>
                )}
                <Modal
                    className='VoteContent'
                    aria-labelledby="transition-modal-title"
                    aria-describedby="transition-modal-description"
                    open={open}
                    onClose={handleClose}
                    closeAfterTransition
                    slots={{ backdrop: Backdrop }}
                    slotProps={{
                        backdrop: {
                            timeout: 500,
                        },
                    }}
                >
                    <Fade in={open} className='VoteGivenBox'>
                        <Box sx={style} className="MessageBox">
                            <h2>Congratulations! </h2>
                            <h5>You Have Successfully Voted</h5>
                            <button onClick={handleClose}><a href="/User">Ok</a></button>
                        </Box>
                    </Fade>
                </Modal>
                <TableContainer component={Paper} ref={revealRefBottom}>
                    <Table sx={{ minWidth: 200 }} aria-label="customized table">
                        <TableHead>
                            <TableRow className='TableRow'>
                                {columns.map((column) => (
                                    <TableCell className='table_row_heading'
                                        key={column.id}
                                        align={column.align}
                                        style={{ minWidth: column.minWidth }}
                                    >
                                        {column.label}
                                    </TableCell>
                                ))}

                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {candidate.map((row) => (
                                <StyledTableRow key={row.name}>
                                    <StyledTableCell>
                                        <span className='Name-Row image'>
                                            <img
                                                alt={row.fullName}
                                                src={row.image.startsWith('default')
                                                    ? '/assets/default-candidate.jpg'
                                                    : `${BASE_URL}/uploads/${row.image}`}
                                                style={{ width: '50px', height: '50px', objectFit: 'cover', borderRadius: '50%' }}
                                            />
                                        </span>
                                        <span className='Name-Row text' align='left'>{row.fullName}</span>
                                    </StyledTableCell>
                                    <StyledTableCell align='left'>{row.party}</StyledTableCell>
                                    <StyledTableCell align="center">{row.age}</StyledTableCell>
                                    <StyledTableCell align="right" className='Symbol'>
                                        <img
                                            alt={`${row.party} symbol`}
                                            src={row.symbol.startsWith('default')
                                                ? '/assets/default-symbol.jpg'
                                                : `${BASE_URL}/uploads/${row.symbol}`}
                                            style={{ width: '50px', height: '50px', objectFit: 'contain' }}
                                        />
                                    </StyledTableCell>
                                    <StyledTableCell align="right" className="voteButton">
                                        <Button variant="contained" className="voteButton" onClick={() => handleVote(row._id)}>Vote</Button>
                                    </StyledTableCell>
                                </StyledTableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </div>

        </div>
    );
}